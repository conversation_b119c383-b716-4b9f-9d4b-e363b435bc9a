import React, { useEffect, useRef } from 'react';
import { Container, Row, Col, Button } from 'react-bootstrap';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import Version1 from "../Asserts/Version1.png";
import {
    FaCheckCircle, FaRocket, FaIndustry, FaRulerCombined,
    FaProjectDiagram, FaBolt, FaArrowRight
} from 'react-icons/fa';

gsap.registerPlugin(ScrollTrigger);

const physicalVerificationData = {
    hero: {
        title: "Physical Verification",
        subtitle:
            "Ensuring your semiconductor designs meet all manufacturing and quality standards with unmatched precision and reliability. Our comprehensive verification process minimizes errors and maximizes production efficiency.",
    },
    intro: {
        title: "Comprehensive Physical Verification Solutions",
        description:
            "Our physical verification services include Design Rule Checking (DRC), Layout Versus Schematic (LVS), Electrical Rule Checking (ERC), and antenna checks to guarantee your chip's manufacturability and reliability. We ensure your design is robust and production-ready.",
        image: Version1,
    },
    services: [
        {
            id: "drc",
            title: "Design Rule Checking (DRC)",
            description:
                "Verifying that the layout strictly adheres to the foundry's design rules is the most critical step to prevent manufacturing defects and ensure high yield.",
            features: [
                "Foundry rule deck development & customization",
                "Automated, high-speed DRC runs & sign-off",
                "Efficient error analysis and clear reporting",
                "Expertise in both hierarchical & flat DRC methodologies",
            ],
            icon: FaRulerCombined,
            image:
                "https://images.unsplash.com/photo-1591696205602-2f950c417cb9?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80",
        },
        {
            id: "lvs",
            title: "Layout Versus Schematic (LVS)",
            description:
                "We ensure the physical layout is a perfect electrical match to the original schematic netlist. This crucial check prevents functional failures and guarantees design integrity.",
            features: [
                "Accurate netlist extraction for complex designs",
                "Comprehensive layout-to-schematic comparison",
                "Efficient mismatch resolution and debugging",
                "Handling complex hierarchical and mixed-signal designs",
            ],
            icon: FaProjectDiagram,
            image:
                "https://images.unsplash.com/photo-1518773553398-650c184e0bb3?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80",
        },
        {
            id: "erc",
            title: "Electrical Rule Checking (ERC)",
            description:
                "Our team performs critical checks for electrical violations like antenna effects, floating nodes, and power integrity issues that can severely impact chip reliability and lifespan.",
            features: [
                "Advanced antenna effect checks & automated fixing",
                "Robust power network and pin connectivity verification",
                "Signal integrity analysis for noise and crosstalk",
                "Comprehensive Electrostatic Discharge (ESD) verification",
            ],
            icon: FaBolt,
            image:
                "https://images.unsplash.com/photo-1605647540924-852290ab8b78?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80",
        },
    ],
    benefits: [
        {
            icon: FaRocket,
            title: "Reduced Time-to-Market",
            desc:
                "Our expert verification process catches critical errors early, preventing costly tape-out spins and accelerating your product launch.",
        },
        {
            icon: FaCheckCircle,
            title: "Maximized Yield & Reliability",
            desc:
                "By ensuring strict adherence to all rules, we help you achieve higher manufacturing yields and produce more reliable silicon.",
        },
        {
            icon: FaIndustry,
            title: "Foundry Compliance Guarantee",
            desc:
                "With deep expertise in various foundry rule decks, we ensure your design will be accepted and manufactured without compliance issues.",
        },
    ],
    cta: {
        title: "Ready to Ensure Flawless Silicon?",
        description:
            "Partner with us to leverage our industry-leading physical verification expertise. Let's build the future of semiconductor technology together.",
        buttonText: "Contact Us",
    },
};

const accentRgb = "0, 160, 233";
const backgroundColor = "#001a35";

// --- Main heading style remains consistent ---
const headingStyle = {
    fontSize: "3rem",
    fontWeight: "800",
    letterSpacing: "2.6px",
    marginBottom: "3rem",
    background: "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
    WebkitBackgroundClip: "text",
    WebkitTextFillColor: "transparent",
    backgroundClip: "text",
    textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
};


const PhysicalVerificationPage = () => {
    const pageRef = useRef(null);

    useEffect(() => {
        window.scrollTo(0, 0);

        const ctx = gsap.context(() => {
            // Updated GSAP animation targets for the new hero section
            gsap.timeline()
                .from(".hero-title", { opacity: 0, y: 50, duration: 0.8, ease: 'power3.out' })
                .from(".hero-subtitle", { opacity: 0, y: 40, duration: 0.8, ease: 'power3.out' }, "-=0.6");

            gsap.utils.toArray(".animated-section").forEach((section) => {
                gsap.from(section, {
                    opacity: 0,
                    y: 100,
                    duration: 1,
                    ease: "power3.out",
                    scrollTrigger: {
                        trigger: section,
                        start: "top 85%",
                        toggleActions: "play none none none",
                    },
                });
            });
        }, pageRef);

        return () => ctx.revert();
    }, []);

    return (
        <div
            ref={pageRef}
            style={{
                background: backgroundColor,
                color: "#e0e0e0",
                overflowX: "hidden", // This is the only overflow property needed for the page
            }}
        >
            {/* --- REPLACED HERO SECTION --- */}
            <section
                className="d-flex align-items-center position-relative text-center"
                style={{ height: "100vh", overflow: 'hidden' }} // overflow: hidden here contains the scaled background
            >
                {/* Background Image & Overlay */}
                <div style={{
                    position: 'absolute', top: 0, left: 0, width: '100%', height: '100%',
                    backgroundImage: `url(${Version1})`, backgroundSize: 'cover', backgroundPosition: 'center',
                    filter: 'blur(8px) brightness(0.4)', transform: 'scale(1.1)', zIndex: 0,
                }}></div>
                <div style={{
                    position: 'absolute', top: 0, left: 0, width: '100%', height: '100%',
                    backgroundColor: 'rgba(0, 26, 53, 0.6)', zIndex: 1,
                }}></div>

                <Container style={{ zIndex: 2 }}>
                    <h1 className="fw-bolder display-2 mb-4 hero-title" style={{
                        background: `linear-gradient(135deg, #ffffff 0%, rgba(${accentRgb}, 1) 100%)`,
                        WebkitBackgroundClip: "text", WebkitTextFillColor: "transparent",
                        textShadow: `0 0 40px rgba(${accentRgb}, 0.5)`
                    }}>
                        {physicalVerificationData.hero.title}
                    </h1>
                    <p className="lead mb-0 mx-auto hero-subtitle" style={{ maxWidth: "800px", color: "#c0c0c0", fontSize: '1.25rem' }}>
                        {physicalVerificationData.hero.subtitle}
                    </p>
                </Container>
            </section>

            {/* --- Your Existing Intro Section (with minor style tweaks for consistency) --- */}
            <section className="py-5 animated-section">
                <Container>
                    <Row className="align-items-center g-5">
                        <Col lg={6}>
                            <h2 style={headingStyle}>
                                {physicalVerificationData.intro.title}
                            </h2>
                            <p
                                className="lead"
                                style={{ fontSize: "1.3rem", color: "#ccc", lineHeight: 1.7 }}
                            >
                                {physicalVerificationData.intro.description}
                            </p>
                        </Col>
                        <Col lg={6}>
                            <div
                                className="p-4 rounded-4"
                                style={{
                                    background: `rgba(0,0,0,0.5)`,
                                    backdropFilter: "blur(10px)",
                                    border: `1px solid rgba(${accentRgb},0.3)`,
                                }}
                            >
                                {[
                                    {
                                        title: "Design Rule Checking (DRC)",
                                        desc: "Ensures manufacturability by checking against foundry design rules.",
                                        icon: FaRulerCombined,
                                    },
                                    {
                                        title: "Layout Versus Schematic (LVS)",
                                        desc: "Guarantees functional correctness by matching layout to the schematic.",
                                        icon: FaProjectDiagram,
                                    },
                                    {
                                        title: "ERC & Antenna",
                                        desc: "Prevents reliability issues by checking for electrical and process-related violations.",
                                        icon: FaBolt,
                                    },
                                ].map((item, idx, arr) => (
                                    <div
                                        key={idx}
                                        className="d-flex align-items-start"
                                        style={{
                                            borderBottom:
                                                idx < arr.length - 1
                                                    ? `1px solid rgba(${accentRgb},0.2)`
                                                    : "none",
                                            paddingBottom: idx < arr.length - 1 ? "1rem" : 0,
                                            marginBottom: idx < arr.length - 1 ? "1rem" : 0,
                                        }}
                                    >
                                        <item.icon
                                            size={40}
                                            className="me-3 flex-shrink-0"
                                            style={{
                                                color: `rgba(${accentRgb},1)`,
                                            }}
                                        />
                                        <div>
                                            <h5
                                                className="fw-bold mb-1"
                                                style={{
                                                    color: `rgba(${accentRgb},1)`,
                                                }}
                                            >
                                                {item.title}
                                            </h5>
                                            <p
                                                className="mb-0"
                                                style={{ color: "#c0c0c0" }}
                                            >
                                                {item.desc}
                                            </p>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </Col>
                    </Row>
                </Container>
            </section>


            {/* --- Your Existing Services Section --- */}
            <section className="py-5">
                <Container>
                    <h2
                        className="text-center mb-5 animated-section"
                        style={headingStyle}
                    >
                        Our Services
                    </h2>
                    <Row className="g-5">
                        {physicalVerificationData.services.map(
                            (service) => (
                                <Col lg={4} md={6} key={service.id} className="d-flex">
                                    <div
                                        className="rounded-4 h-100 d-flex flex-column service-card"
                                        style={{
                                            background: `rgba(6, 31, 59, 0.6)`,
                                            border: `1px solid rgba(${accentRgb},0.3)`,
                                            transition: "all 0.3s ease",
                                            cursor: "pointer",
                                        }}
                                    >
                                        <img
                                            src={service.image}
                                            alt={service.title}
                                            className="img-fluid rounded-top-4"
                                            style={{ objectFit: 'cover', height: '200px' }}
                                        />
                                        <div className="p-4 flex-grow-1 d-flex flex-column">
                                            <h4
                                                className="fw-bold mb-3"
                                                style={{
                                                    color: `rgba(${accentRgb},1)`,
                                                }}
                                            >
                                                {service.title}
                                            </h4>
                                            <p className="flex-grow-1">
                                                {service.description}
                                            </p>
                                            <ul className="list-unstyled mt-3 mb-0">
                                                {service.features.map(
                                                    (feature, fIdx) => (
                                                        <li
                                                            key={fIdx}
                                                            className="d-flex align-items-start mb-2"
                                                        >
                                                            <FaCheckCircle
                                                                className="me-3 mt-1 flex-shrink-0"
                                                                style={{
                                                                    color: `rgba(${accentRgb},1)`,
                                                                }}
                                                            />
                                                            <span>{feature}</span>
                                                        </li>
                                                    )
                                                )}
                                            </ul>
                                        </div>
                                    </div>
                                </Col>
                            )
                        )}
                    </Row>
                </Container>
            </section>


            {/* --- Your Existing Benefits Section --- */}
            <section className="py-5 animated-section">
                <Container>
                    <h2
                        className="text-center mb-5"
                        style={headingStyle}
                    >
                        Key Benefits
                    </h2>
                    <Row className="g-4">
                        {physicalVerificationData.benefits.map((item, idx) => (
                            <Col md={4} key={idx} className="d-flex">
                                <div
                                    className="text-center p-4 rounded-3 h-100 benefit-card"
                                    style={{
                                        background: `rgba(${accentRgb},0.05)`,
                                        border: `1px solid rgba(${accentRgb},0.2)`,
                                        transition: "all 0.3s ease",
                                    }}
                                >
                                    <div
                                        className="mb-3 d-inline-flex justify-content-center align-items-center"
                                        style={{
                                            width: "80px",
                                            height: "80px",
                                            borderRadius: "50%",
                                            background: `linear-gradient(135deg, rgba(${accentRgb},0.1), rgba(${accentRgb},0.3))`,
                                            color: `rgba(${accentRgb},1)`,
                                        }}
                                    >
                                        <item.icon size={40} />
                                    </div>
                                    <h5
                                        className="fw-bold"
                                        style={{
                                            color: `rgba(${accentRgb},1)`,
                                        }}
                                    >
                                        {item.title}
                                    </h5>
                                    <p>{item.desc}</p>
                                </div>
                            </Col>
                        ))}
                    </Row>
                </Container>
            </section>

            
            {/* Added CSS for hover effects for a more polished feel */}
            <style>{`
                .service-card:hover {
                    transform: translateY(-10px);
                    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
                    border-color: rgba(${accentRgb}, 0.5);
                }
                .benefit-card:hover {
                    transform: translateY(-5px);
                    background: rgba(${accentRgb},0.1) !important;
                    border-color: rgba(${accentRgb}, 0.4) !important;
                }
                .cta-button:hover {
                    transform: translateY(-3px);
                    box-shadow: 0 10px 20px rgba(0, 160, 233, 0.4);
                }
            `}</style>
        </div>
    );
};

export default PhysicalVerificationPage;