import React, { useEffect, useRef, useState } from "react";
import 'bootstrap/dist/css/bootstrap.min.css';
import ExpertiseImage from "../Asserts/PD3.png"; // Make sure this path is correct
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

// Importing icons for a better UI
import { FaMicrochip, FaRulerCombined, FaBolt, FaCheckCircle, FaProjectDiagram, FaBalanceScale, FaCogs, FaTasks } from 'react-icons/fa';

gsap.registerPlugin(ScrollTrigger);

// Color constants for a consistent theme
const accentRgb = '0, 160, 233';
const backgroundColor = '#001a35';

// Centralized style for all section headings
const headingStyle = {
    fontSize: "3rem",
    fontWeight: "800",
    letterSpacing: "2.6px",
    marginBottom: "3rem",
    background: "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
    WebkitBackgroundClip: "text",
    WebkitTextFillColor: "transparent",
    backgroundClip: "text",
    textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
};

const PhysicalDesignPage = () => {
    const [activeTab, setActiveTab] = useState('implementation');
    const pageRef = useRef(null);
    const tabContentRef = useRef(null);

    // Data arrays
    const expertiseTabs = {
        implementation: {
            title: "Full-Chip Physical Implementation",
            shortTitle: "Implementation",
            icon: FaRulerCombined,
            description: "From netlist to a production-ready GDSII, we handle the complete physical implementation flow, focusing on PPA optimization at every stage.",
            services: [
                { title: "Floorplanning & Power Planning", desc: "Strategic placement of macros and a robust power grid for optimal power distribution." },
                { title: "Placement & Routing", desc: "Advanced placement and routing to minimize congestion and wire length." },
                { title: "Clock Tree Synthesis (CTS)", desc: "Building low-skew, low-power clock trees for timing synchronization." },
                { title: "Signal & Power Integrity", desc: "Crosstalk analysis, noise mitigation, and EM/IR analysis for chip reliability." },
            ]
        },
        analysis: {
            title: "Static Timing & Power Analysis",
            shortTitle: "Analysis",
            icon: FaBalanceScale,
            description: "Rigorous analysis to ensure the design meets its timing, power, and performance specifications under all operating conditions.",
            services: [
                { title: "Constraint Development (SDC)", desc: "Comprehensive SDC creation and validation for accurate timing path analysis." },
                { title: "Timing Sign-off (MMMC)", desc: "Multi-Mode Multi-Corner analysis to guarantee timing closure." },
                { title: "Advanced On-Chip Variation", desc: "Modeling and analysis of process variations to prevent silicon failures." },
                { title: "Power Analysis", desc: "Static and dynamic power analysis to meet power budgets and optimize for low-power operation." },
            ]
        },
        verification: {
            title: "Physical Verification",
            shortTitle: "Verification",
            icon: FaCheckCircle,
            description: "Ensuring the layout is manufacturable, robust, and free of errors before tape-out, using industry-standard tools and methodologies.",
            services: [
                { title: "DRC, LVS, Antenna & ERC", desc: "Comprehensive checks for Design Rule Checking, Layout vs. Schematic, and Electrical Rules." },
                { title: "Design for Manufacturability (DFM)", desc: "Applying advanced DFM rules to improve yield and reliability." },
                { title: "Reliability Checks", desc: "ESD, latch-up, and other checks to ensure long-term device robustness." },
                { title: "GDSII/OASIS Generation", desc: "Final, clean GDSII/OASIS file generation for hand-off to the foundry." },
            ]
        },
        signoff: {
            title: "Full-Chip Sign-off",
            shortTitle: "Sign-off",
            icon: FaTasks,
            description: "Providing final, comprehensive checks and reports to ensure full confidence before committing to manufacturing.",
            services: [
                { title: "Final Validation Reports", desc: "Generating detailed reports for timing, power, and physical verification across all corners." },
                { title: "ECO Implementation", desc: "Handling last-minute Engineering Change Orders efficiently and accurately." },
                { title: "Tapeout Package", desc: "Preparing and delivering a complete, verified tapeout package for the foundry." },
                { title: "Foundry Communication", desc: "Liaising with the foundry to ensure a smooth and successful mask generation process." },
            ]
        }
    };

    const differentiators = [
        { icon: FaMicrochip, title: "Advanced Node Expertise", desc: "Proven success in complex designs on cutting-edge FinFET nodes including 7nm, 5nm, and 3nm." },
        { icon: FaBolt, title: "PPA Optimization Focus", desc: "Our methodology is built around optimizing for the perfect balance of Power, Performance, and Area." },
        { icon: FaProjectDiagram, title: "Flexible Engagement Models", desc: "We offer end-to-end project ownership or team extension models to fit your specific needs." }
    ];

    const methodologySteps = [
        { title: "Project Kick-off & Spec Review", desc: "Aligning on project goals, design specs, and defining clear milestones and deliverables." },
        { title: "Floorplanning & Partitioning", desc: "Creating an optimal physical hierarchy and power strategy as the foundation for success." },
        { title: "Iterative Implementation & Analysis", desc: "Continuous cycles of implementation (P&R, CTS) and analysis (STA) to converge on PPA targets." },
        { title: "Rigorous Sign-off & Tape-out", desc: "Final physical and timing verification across all corners before generating the final GDSII." },
    ];

    // Animation Effect for Tab Content
    useEffect(() => {
        if (tabContentRef.current) {
            gsap.timeline()
                .to(tabContentRef.current.children, { autoAlpha: 0, y: 20, duration: 0.2, stagger: 0.05, })
                .set(tabContentRef.current.children, { y: -20 })
                .to(tabContentRef.current.children, { autoAlpha: 1, y: 0, duration: 0.4, stagger: 0.1, delay: 0.1 });
        }
    }, [activeTab]);

    useEffect(() => {
        document.title = "Physical Design Services | Makonis";
        window.scrollTo(0, 0);
    }, []);

    return (
        <div ref={pageRef} style={{ background: backgroundColor, color: "#e0e0e0" }}>
            {/* Hero Section */}
            <section
                className="d-flex align-items-center position-relative text-center"
                style={{ height: "100vh", padding: "80px 20px", overflow: 'hidden' }}
            >
                <div style={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%', backgroundImage: `url(${ExpertiseImage})`, backgroundSize: 'cover', backgroundPosition: 'center', filter: 'blur(8px) brightness(0.4)', transform: 'scale(1.1)', zIndex: 0 }}></div>
                <div style={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%', backgroundColor: 'rgba(0, 26, 53, 0.6)', zIndex: 1, }}></div>
                <div className="container" style={{ zIndex: 2 }}>
                    <h1 className="fw-bolder display-2 mb-3 hero-title" style={{ background: `linear-gradient(135deg, #ffffff 0%, rgba(${accentRgb}, 1) 100%)`, WebkitBackgroundClip: "text", WebkitTextFillColor: "transparent", textShadow: `0 0 40px rgba(${accentRgb}, 0.4)` }}>
                        From Concept to Silicon. Perfected.
                    </h1>
                    <p className="lead mb-4 mx-auto hero-subtitle" style={{ maxWidth: "1000px", color: "#c0c0c0", fontSize: '1.5rem' }}>
                        We provide industry-leading Physical Design services, transforming your RTL into high-performance, power-efficient, and area-optimized GDSII for the most advanced semiconductor nodes.
                    </p>
                </div>
            </section>

            {/* Expertise Section with Tabs */}
            <section className="py-5 animated-section">
                <div className="container">
                    <div className="text-center">
                        <h2 style={headingStyle}>Our Core Expertise</h2>
                    </div>
                    <div className="expertise-tabs">
                        <div className="nav nav-pills justify-content-center mb-5">
                            {Object.keys(expertiseTabs).map(key => {
                                const IconComponent = expertiseTabs[key].icon;
                                return (
                                    <button key={key} className={`nav-link ${activeTab === key ? 'active' : ''}`} onClick={() => setActiveTab(key)}>
                                        <IconComponent /> <span>{expertiseTabs[key].shortTitle}</span>
                                    </button>
                                );
                            })}
                        </div>
                        <div className="tab-content">
                            <div ref={tabContentRef} className="row g-4">
                                <div className="col-12 tab-description">
                                    <p className="lead mb-4">{expertiseTabs[activeTab].description}</p>
                                </div>
                                {expertiseTabs[activeTab].services.map((service, index) => (
                                    <div key={index} className="col-md-6 service-card-item">
                                        <div className="service-card h-100 p-4">
                                            <h5 className="fw-bold d-flex align-items-center">
                                                <FaCogs size={20} className="me-3" style={{ color: `rgba(${accentRgb}, 0.7)` }} />
                                                <span style={{ color: `rgba(${accentRgb}, 1)` }}>{service.title}</span>
                                            </h5>
                                            <p className="mb-0 ps-4 ms-3 service-desc">{service.desc}</p>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Other sections... */}
            <section className="py-5 animated-section">
                <div className="container">
                    <div className="text-center">
                        <h2 style={headingStyle}>Why Partner With Us?</h2>
                    </div>
                    <div className="row g-4 text-center">
                        {differentiators.map((item, idx) => (
                            <div key={idx} className="col-md-4">
                                <div className="differentiator-card p-4 h-100">
                                    <div className="icon-wrapper mb-3">
                                        <item.icon size={40} />
                                    </div>
                                    <h4 className="fw-bold mb-2">{item.title}</h4>
                                    <p>{item.desc}</p>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </section>
            <section className="py-5 animated-section">
                <div className="container">
                    <div className="text-center">
                        <h2 style={headingStyle}>Our Methodology</h2>
                        <p className="lead mx-auto" style={{ maxWidth: '700px', marginTop: '-2rem' }}>A proven, systematic approach to ensure first-pass silicon success.</p>
                    </div>
                    <div className="methodology-timeline position-relative">
                        {methodologySteps.map((step, idx) => (
                            <div key={idx} className="timeline-item">
                                <div className="timeline-step-number">{idx + 1}</div>
                                <div className="timeline-card p-4">
                                    <h5 className="fw-bold">{step.title}</h5>
                                    <p className="mb-0">{step.desc}</p>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            <style>
                {`
                /* MODIFIED: Pill-shaped tab styling */
                .expertise-tabs .nav-pills .nav-link {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 0.75rem; /* Increased gap for better spacing */
                    color: #c0d0e0; /* Lighter color for inactive tabs */
                    background: transparent;
                    border: 1px solid rgba(${accentRgb}, 0.3); /* Subtle border for inactive tabs */
                    margin: 0 8px;
                    padding: 12px 28px; /* More padding for a bigger button */
                    border-radius: 50px; /* Fully rounded corners */
                    transition: all 0.3s ease;
                    font-weight: 500;
                    font-size: 1.1rem;
                }

                .expertise-tabs .nav-pills .nav-link:hover {
                    color: #ffffff;
                    border-color: rgba(${accentRgb}, 0.7);
                    background: rgba(${accentRgb}, 0.1);
                }

                .expertise-tabs .nav-pills .nav-link.active {
                    color: #ffffff;
                    background: rgba(${accentRgb}, 1); /* Solid background for active tab */
                    border-color: rgba(${accentRgb}, 1);
                    font-weight: 600;
                    box-shadow: 0 5px 20px -5px rgba(${accentRgb}, 0.5); /* Shadow for active tab */
                }
                
                /* Other styles remain the same */
                .tab-content { background: rgba(0,0,0,0.1); border: 1px solid rgba(${accentRgb}, 0.15); border-radius: 8px; padding: 2.5rem; box-shadow: inset 0 0 40px rgba(0,0,0,0.2); }
                .tab-description { text-align: center; border-bottom: 1px solid rgba(${accentRgb}, 0.1); padding-bottom: 1.5rem; margin-bottom: 1.5rem !important; }
                .tab-description .lead { font-size: 1.25rem; max-width: 900px; margin-left: auto; margin-right: auto; color: #c0c8d8; }
                .service-card { background: transparent; border-radius: 10px; border: none; border-left: 3px solid rgba(${accentRgb}, 0.2); transition: all 0.3s ease; }
                .service-card:hover { transform: translateX(5px); background: rgba(${accentRgb}, 0.05); border-left-color: rgba(${accentRgb}, 1); box-shadow: 0 5px 25px rgba(${accentRgb}, 0.1); }
                .service-desc { border-left: 1px solid rgba(${accentRgb}, 0.2); padding-left: 1.2rem !important; color: #aab8c8; }
                .differentiator-card { background: rgba(${accentRgb}, 0.05); border-radius: 15px; border-bottom: 3px solid transparent; transition: all 0.3s ease; }
                .differentiator-card:hover { transform: translateY(-10px); background: rgba(${accentRgb}, 0.1); border-bottom-color: rgba(${accentRgb}, 1); }
                .differentiator-card .icon-wrapper { display: inline-flex; justify-content: center; align-items: center; width: 80px; height: 80px; border-radius: 50%; background: linear-gradient(135deg, rgba(${accentRgb}, 0.1), rgba(${accentRgb}, 0.2)); color: rgba(${accentRgb}, 1); box-shadow: 0 0 20px rgba(${accentRgb}, 0.2); }
                .methodology-timeline { padding-left: 50px; border-left: 3px solid rgba(${accentRgb}, 0.2); }
                .timeline-item { position: relative; margin-bottom: 50px; }
                .timeline-item:last-child { margin-bottom: 0; }
                .timeline-step-number { position: absolute; left: -75px; top: 50%; transform: translateY(-50%); width: 50px; height: 50px; border-radius: 50%; background: linear-gradient(135deg, rgba(${accentRgb}, 1), #005eff); color: #fff; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 1.2rem; border: 3px solid ${backgroundColor}; box-shadow: 0 0 15px rgba(${accentRgb}, 0.4); }
                .timeline-card { background: rgba(0,0,0,0.2); border-radius: 10px; border-left: 4px solid rgba(${accentRgb}, 1); }
                @media (max-width: 768px) { h2 { font-size: 2.2rem !important; } }
                `}
            </style>
        </div>
    );
};

export default PhysicalDesignPage;