import React, { useEffect, useRef, useState } from "react";
import 'bootstrap/dist/css/bootstrap.min.css';
import ExpertiseImage from "../Asserts/PD3.png"; // Make sure this path is correct
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger); //

// Define color constants outside the component for reusability and clarity
const accentRgb = '0, 160, 233'; // RGB values for the accent color
const primaryColor = '#002956'; // Deep Blue - Used for the main background

const PhysicalDesignPage = () => {
  const heroRef = useRef(null); //
  const centralChipRef = useRef(null); //
  const titleRef = useRef(null); //
  const subtitleRef = useRef(null); //
  const ctaButtonRef = useRef(null); //
  const [isLoading, setIsLoading] = useState(false); //

  useEffect(() => {
    document.title = "Physical Design | Makonis"; //
  }, []); //

  useEffect(() => {
    if (!isLoading) { //
      const ctx = gsap.context(() => { //
        const tl = gsap.timeline({ delay: 0.3 }); //

        tl.from(centralChipRef.current, { //
          opacity: 0, //
          scale: 0.5, //
          duration: 1.2, //
          ease: "power3.out" //
        })
          .from(titleRef.current, { //
            y: 100, //
            opacity: 0, //
            duration: 1, //
            ease: "power3.out" //
          }, "-=0.8") //
          .from(subtitleRef.current, { //
            y: 50, //
            opacity: 0, //
            duration: 0.8, //
            ease: "power2.out" //
          }, "-=0.7") //
          .from(ctaButtonRef.current, { //
            opacity: 0, //
            y: 30, //
            duration: 0.7, //
            ease: "power2.out" //
          }, "-=0.5"); //

        gsap.from("#intro-section .animated-col", { //
          x: (i) => i % 2 === 0 ? -80 : 80, //
          opacity: 0, //
          duration: 1.2, //
          ease: "power3.out", //
          stagger: 0.25, //
          scrollTrigger: { //
            trigger: "#intro-section", //
            start: "top 80%", //
            toggleActions: "play none none none", //
          }
        }); //
        gsap.from("#intro-section .intro-image-wrapper", { //
          opacity: 0, //
          scale: 0.8, //
          duration: 1, //
          ease: "power3.out", //
          scrollTrigger: { //
            trigger: "#intro-section", //
            start: "top 75%", //
            toggleActions: "play none none none", //
          }
        }); //

        gsap.from("#services-section .section-header-animate", { //
          y: 70, //
          opacity: 0, //
          duration: 1, //
          ease: "power2.out", //
          scrollTrigger: { //
            trigger: "#services-section", //
            start: "top 80%", //
            toggleActions: "play none none none", //
          }
        }); //

        // GSAP for timeline items and lines
        gsap.from(".timeline-item", { //
            opacity: 0, //
            y: 50, //
            duration: 0.8, //
            ease: "power2.out", //
            stagger: 0.2, //
            scrollTrigger: { //
                trigger: "#services-section .timeline", //
                start: "top 75%", //
                toggleActions: "play none none none", //
            }
        }); //
        gsap.from(".timeline-vertical-line", { //
            height: 0, //
            duration: 1.5, //
            ease: "power2.inOut", //
            scrollTrigger: { //
                trigger: "#services-section .timeline", //
                start: "top 75%", //
                toggleActions: "play none none none", //
            }
        }); //

        // Animate the new snake line elements
        gsap.from(".timeline-item .snake-horizontal", { //
            scaleX: 0, //
            duration: 0.6, //
            ease: "power1.out", //
            stagger: 0.2, //
            scrollTrigger: { //
                trigger: "#services-section .timeline", //
                start: "top 70%", //
                toggleActions: "play none none none", //
            }
        }); //

        gsap.from(".timeline-item .snake-vertical", { //
            scaleY: 0, //
            duration: 0.6, //
            ease: "power1.out", //
            stagger: 0.2, //
            scrollTrigger: { //
                trigger: "#services-section .timeline", //
                start: "top 70%", //
                toggleActions: "play none none none", //
            }
        }); //


        if (heroRef.current) { //
          const particles = heroRef.current.querySelectorAll('.hero-particle'); //
          particles.forEach(particle => { //
            gsap.set(particle, { //
              x: gsap.utils.random(-window.innerWidth / 2, window.innerWidth / 2), //
              y: gsap.utils.random(-window.innerHeight / 2, window.innerHeight / 2), //
              scale: gsap.utils.random(0.3, 1), //
              opacity: 0, //
            }); //
            gsap.to(particle, { //
              x: `+=${gsap.utils.random(-100, 100)}`, //
              y: `+=${gsap.utils.random(-100, 100)}`, //
              opacity: gsap.utils.random(0.1, 0.5), //
              duration: gsap.utils.random(5, 15), //
              repeat: -1, //
              yoyo: true, //
              ease: "sine.inOut", //
              delay: gsap.utils.random(0, 5) //
            }); //
          }); //
        }

      }, heroRef); //

      return () => ctx.revert(); //
    }
  }, [isLoading]); //

  const processSteps = [ //
    { //
      step: "01", //
      title: "Schematic Extraction", //
      desc: "We extract the schematic view of the design and run migration scripts from the source to the destination database." //
    },
    { //
      step: "02", //
      title: "Block Mapping", //
      desc: "Verify device mapping for all blocks, simulate with destination models, and create a comparison table." //
    },
    { //
      step: "03", //
      title: "Layout Migration", //
      desc: "Migrate layout views with manual tweaks and initiate verification flows after simulation checks." //
    },
    { //
      step: "04", //
      title: "Manual Porting", //
      desc: "If mismatches arise, apply manual porting techniques and replicate transistor operating points in new tech." //
    },
    { //
      step: "05", //
      title: "Comparison & Tuning", //
      desc: "Tweak and match source characteristics to the destination, ensuring the design meets specifications." //
    },
    { //
      step: "06", //
      title: "Final Netlist Verification", //
      desc: "Post-layout netlist is compared with pre-layout simulation to confirm requirement alignment." //
    }
  ]; //

  return (
    <div style={{ background: "linear-gradient(135deg, rgba(0, 41, 86, 0.95) 0%, rgba(0, 41, 86, 0.98) 100%)", backdropFilter: "blur(10px)", color: "#ffffff" }}>

      {/* Hero Section (No change) */}
      <section
        ref={heroRef}
        className="d-flex align-items-center position-relative"
        style={{
          background: "linear-gradient(135deg, rgba(0, 41, 86, 0.95) 0%, rgba(0, 41, 86, 0.98) 100%)",
          backdropFilter: "blur(10px)",
          minHeight: "520px",
          padding: "60px 20px",
          overflow: "hidden"
        }}
      >
        <div className="position-absolute w-100 h-100 top-0 start-0" style={{ zIndex: 0 }}>
          {[...Array(50)].map((_, i) => (
            <div
              key={`particle-${i}`}
              className="hero-particle position-absolute"
              style={{
                width: `${Math.random() * 3 + 1}px`,
                height: `${Math.random() * 3 + 1}px`,
                background: `rgba(${accentRgb}, ${Math.random() * 0.5 + 0.2})`,
                borderRadius: '50%',
                boxShadow: `0 0 ${Math.random() * 6 + 2}px rgba(${accentRgb}, 0.5)`,
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
            />
          ))}
          {[...Array(8)].map((_, i) => (
            <div
              key={`tech-element-${i}`}
              className="floating-tech-element"
              style={{
                top: `${Math.random() * 80 + 10}%`,
                left: `${Math.random() * 80 + 10}%`,
                width: `${30 + Math.random() * 40}px`,
                height: `${30 + Math.random() * 40}px`,
                background: `rgba(${accentRgb}, ${0.05 + Math.random() * 0.1})`,
                borderRadius: i % 3 === 0 ? '50%' : i % 3 === 1 ? '0%' : '25%',
                animationDelay: `${Math.random() * 5}s`,
                border: `1px solid rgba(${accentRgb}, 0.2)`,
              }}
            />
          ))}
          {[...Array(3)].map((_, i) => (
            <div
              key={`morph-bg-${i}`}
              className="morphing-bg-element"
              style={{
                top: `${20 + i * 25}%`,
                right: `${10 + i * 20}%`,
                width: `${80 + i * 30}px`,
                height: `${80 + i * 30}px`,
                animationDelay: `${i * 3}s`,
              }}
            />
          ))}
          <div
            ref={centralChipRef}
            className="position-absolute glow-pulse"
            style={{
              width: '200px',
              height: '200px',
              zIndex: 1,
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -calc(50% + 50px))',
              opacity: 0.4,
            }}
          >
            <div className="central-chip-core" style={{
              width: '100%', height: '100%',
              borderRadius: '25px',
              background: `radial-gradient(circle, rgba(${accentRgb}, 0.4) 0%, rgba(${accentRgb}, 0.08) 60%)`,
              boxShadow: `0 0 40px rgba(${accentRgb},0.3), inset 0 0 20px rgba(${accentRgb},0.15)`,
              animation: 'pulseCore 3s infinite ease-in-out',
              position: 'relative',
              border: `2px solid rgba(${accentRgb}, 0.3)`,
            }}>
              <div style={{ position: 'absolute', top: '25%', left: '25%', width: '50%', height: '50%', background: `rgba(${accentRgb}, 0.3)`, borderRadius: '10px', boxShadow: `0 0 20px rgba(${accentRgb},0.4)`, border: `1px solid rgba(${accentRgb}, 0.5)`, }}></div>
              <div style={{ position: 'absolute', top: '10%', left: '10%', width: '15%', height: '15%', background: `rgba(${accentRgb}, 0.2)`, borderRadius: '50%', boxShadow: `0 0 10px rgba(${accentRgb},0.3)`, }}></div>
              <div style={{ position: 'absolute', top: '10%', right: '10%', width: '15%', height: '15%', background: `rgba(${accentRgb}, 0.2)`, borderRadius: '50%', boxShadow: `0 0 10px rgba(${accentRgb},0.3)`, }}></div>
              <div style={{ position: 'absolute', bottom: '10%', left: '10%', width: '15%', height: '15%', background: `rgba(${accentRgb}, 0.2)`, borderRadius: '50%', boxShadow: `0 0 10px rgba(${accentRgb},0.3)`, }}></div>
              <div style={{ position: 'absolute', bottom: '10%', right: '10%', width: '15%', height: '15%', background: `rgba(${accentRgb}, 0.2)`, borderRadius: '50%', boxShadow: `0 0 10px rgba(${accentRgb},0.3)`, }}></div>
            </div>
            {[0, 45, 90, 135, 180, 225, 270, 315].map(angle => (
              <div key={angle} style={{
                position: 'absolute', top: '50%', left: '50%',
                width: '150%', height: '2px',
                background: `linear-gradient(90deg, transparent, rgba(${accentRgb}, 0.3), transparent)`,
                transformOrigin: '0% 50%',
                transform: `translate(0, -1px) rotate(${angle}deg) translateX(50%)`,
                opacity: 0.8,
                boxShadow: `0 0 4px rgba(${accentRgb}, 0.4)`,
              }}></div>
            ))}
          </div>
          <div className="position-absolute w-100 h-100 top-0 start-0"
            style={{
              backgroundImage: `
              linear-gradient(rgba(${accentRgb}, 0.03) 1px, transparent 1px),
              linear-gradient(90deg, rgba(${accentRgb}, 0.03) 1px, transparent 1px)
            `,
              backgroundSize: '30px 30px',
              opacity: 0.5,
            }}
          ></div>
        </div>
        <div className="container" style={{ zIndex: 2 }}>
          <div className="row">
            <div className="col-12 text-center text-white">
              <h1
                ref={titleRef}
                className="fw-bold display-5 mb-3"
                style={{
                  fontSize: "4rem",
                  fontWeight: "800",
                  letterSpacing: "2.6px",
                  marginBottom: "1rem",
                  background: "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
                  WebkitBackgroundClip: "text",
                  WebkitTextFillColor: "transparent",
                  backgroundClip: "text",
                  textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
                }}
              >Physical Design Services</h1>
              <p
                ref={subtitleRef}
                className="lead text-white mb-4"
              >
                From RTL to GDSII, our expert team delivers low-power, high-performance, and area-efficient SoC implementations. We leverage advanced synthesis, place and route, and timing closure techniques to ensure optimal silicon results.
              </p>
              <button
                ref={ctaButtonRef}
                className="btn btn-primary btn-lg mt-3"
                style={{
                  background: `linear-gradient(90deg, rgba(${accentRgb}, 1), #005eff)`,
                  border: 'none',
                  padding: '12px 30px',
                  borderRadius: '50px',
                  fontSize: '1.1rem',
                  fontWeight: 'bold',
                  boxShadow: '0 8px 15px rgba(0,160,233,0.4)',
                  transition: 'all 0.3s ease'
                }}
              >
                Learn More
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Enhanced Expertise Section - Refined Layout (No change) */}
      <section className="py-5" style={{ backgroundColor: "#001e36" }}>
        <div className="container">
          <h2
            className="text-center fw-bold mb-3 section-header-animate"
            style={{
              fontSize: "3rem",
              fontWeight: "800",
              letterSpacing: "2.6px",
              marginBottom: "1rem",
              background: "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              backgroundClip: "text",
              textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
            }}
          >
            Our Expertise
          </h2>
          <p className="text-center text-white mb-5" style={{ maxWidth: "720px", margin: "0 auto" }}>
            From RTL to GDSII, we deliver optimized SoC solutions with precision engineering and domain expertise across all physical design stages.
          </p>

          <div className="row align-items-center g-5">
            <div className="col-lg-6 text-center intro-image-wrapper">
              <div style={{
                padding: "20px",
                borderRadius: "16px",
                background: "linear-gradient(145deg, #0d2a4b, #0c223e)",
                boxShadow: "0 8px 24px rgba(0,160,233,0.1)"
              }}>
                <img
                  src={ExpertiseImage}
                  alt="Chip Visual"
                  className="img-fluid rounded"
                  style={{ maxHeight: "550px", objectFit: "contain" }}
                />
              </div>
            </div>

            <div id="intro-section" className="col-lg-6">
              <div className="row g-4">
                {[
                  {
                    title: "PD Implementation",
                    items: ["Floor Planning", "Place and Route", "Clock Tree Synthesis (CTS)"]
                  },
                  {
                    title: "STA & Design Analysis",
                    items: ["Constraint Generation", "Budgeting", "Timing Sign-off", "Crosstalk, Noise, Signal Integrity", "AOCV and POCV"]
                  },
                  {
                    title: "Physical Verification & DFM",
                    items: ["DRC, LVS, ESD, Antenna Checks", "OPC, CMP, Yield, and Reliability Analysis"]
                  }
                ].map((section, idx) => (
                  <div className="col-md-12 animated-col" key={idx}>
                    <div
                      className="p-4 h-100"
                      style={{
                        borderRadius: "14px",
                        background: "rgba(255,255,255,0.04)",
                        border: "1px solid rgba(0,160,233,0.08)",
                        backdropFilter: "blur(12px)",
                        WebkitBackdropFilter: "blur(12px)",
                        boxShadow: "0 4px 12px rgba(0,160,233,0.05)"
                      }}
                    >
                      <h5 className="text-info fw-bold mb-3">{section.title}</h5>
                      <ul className="text-white ps-3 mb-0">
                        {section.items.map((point, j) => (
                          <li key={j}>{point}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* --- REVISED ALTERNATING TIMELINE/PROCESS STEPS SECTION --- */}
      <section id="services-section" className="py-5" style={{ backgroundColor: "#001e36" }}>
        <div className="container">
          <h2 className="text-center text-info fw-bold mb-5 section-header-animate">The Steps We Follow in Our Approach</h2>
          <div className="row justify-content-center">
            <div className="col-lg-10 col-md-12">
              <div className="timeline position-relative">
                {/* Vertical connecting line */}
                <div className="timeline-vertical-line"></div>
                 {/* Background pattern elements */}
                <div className="timeline-bg-pattern pattern-top-left"></div>
                <div className="timeline-bg-pattern pattern-bottom-right"></div>
                <div className="timeline-bg-pattern pattern-middle-left"></div>
                <div className="timeline-bg-pattern pattern-middle-right"></div>


                {processSteps.map((item, idx) => (
                  <div key={idx} className={`timeline-item ${idx % 2 === 0 ? 'left-aligned' : 'right-aligned'} mb-5`}>
                    <div
                      className="step-indicator flex-shrink-0 d-flex align-items-center justify-content-center"
                      style={{
                        background: `radial-gradient(circle at top left, #00cfff 20%, #005eff 100%)`,
                        color: "#FFFFFF",
                        fontWeight: "bold",
                        fontSize: "1.2rem",
                        boxShadow: `0 0 15px rgba(${accentRgb}, 0.7), inset 0 0 8px rgba(${accentRgb}, 0.4)`,
                        border: `2px solid rgba(${accentRgb}, 0.6)`,
                      }}
                    >
                      {item.step}
                      {/* Separate elements for horizontal and vertical snakes */}
                      <div className="snake-horizontal"></div>
                      <div className="snake-vertical"></div>
                    </div>

                    <div
                      className="process-card p-4 rounded shadow-lg"
                      style={{
                        backgroundColor: "#0f2b4b",
                        border: "1px solid rgba(0,160,233,0.2)",
                        backdropFilter: "blur(8px)",
                        WebkitBackdropFilter: "blur(8px)",
                        transition: "transform 0.3s ease, box-shadow 0.3s ease",
                        textAlign: "center" /* Center content in cards */
                      }}
                    >
                      <h6 className="text-white fw-bold mb-2">{item.title}</h6>
                      <p className="text-white-75" style={{ fontSize: "0.9rem", lineHeight: "1.6" }}>
                        {item.desc}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        <style>
          {`
            /* Hero Section Animations and Styles (Previous) */
            @keyframes pulseCore {
                0% { transform: scale(0.95); box-shadow: 0 0 0 0 rgba(${accentRgb}, 0.4); }
                70% { transform: scale(1); box-shadow: 0 0 0 20px rgba(${accentRgb}, 0); }
                100% { transform: scale(0.95); box-shadow: 0 0 0 0 rgba(${accentRgb}, 0); }
            }
            .floating-tech-element {
              position: absolute;
              opacity: 0.1;
              animation: floatTech 12s ease-in-out infinite;
              pointer-events: none;
            }
            @keyframes floatTech {
              0%, 100% {
                transform: translateY(0) rotate(0deg) scale(1);
                opacity: 0.1;
              }
              50% {
                transform: translateY(-40px) rotate(180deg) scale(1.1);
                opacity: 0.3;
              }
            }
            .glow-pulse {
              animation: glowPulse 3s ease-in-out infinite;
            }
            @keyframes glowPulse {
              0%, 100% {
                filter: drop-shadow(0 0 10px rgba(${accentRgb}, 0.3));
              }
              50% {
                filter: drop-shadow(0 0 25px rgba(${accentRgb}, 0.6));
              }
            }
            .morphing-bg-element {
              position: absolute;
              background: linear-gradient(45deg, rgba(${accentRgb}, 0.05), rgba(${accentRgb}, 0.15));
              border-radius: 50%;
              animation: morphBg 15s ease-in-out infinite;
              filter: blur(2px);
            }
            @keyframes morphBg {
              0%, 100% {
                border-radius: 50%;
                transform: rotate(0deg) scale(1);
              }
              25% {
                border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
                transform: rotate(90deg) scale(1.2);
              }
              50% {
                border-radius: 20% 80% 20% 80% / 80% 20% 80% 20%;
                transform: rotate(180deg) scale(0.8);
              }
              75% {
                border-radius: 70% 30% 30% 70% / 70% 70% 30% 30%;
                transform: rotate(270deg) scale(1.1);
              }
            }


            /* --- REVISED ALTERNATING TIMELINE STYLES --- */
            .timeline {
              position: relative;
              padding: 50px 0; /* Vertical padding */
            }

            .timeline-vertical-line {
              position: absolute;
              left: 50%; /* Center the line */
              top: 0;
              bottom: 0;
              width: 3px;
              background: linear-gradient(to bottom, rgba(${accentRgb}, 0.2), rgba(${accentRgb}, 0.8), rgba(${accentRgb}, 0.2));
              border-radius: 5px;
              transform: translateX(-50%); /* Ensure true center */
              z-index: 0;
            }

            /* Background pattern elements within the timeline */
            .timeline-bg-pattern {
                position: absolute;
                width: 150px;
                height: 150px;
                background: rgba(${accentRgb}, 0.05); /* Very subtle background */
                border-radius: 15px; /* Softer edges */
                filter: blur(5px); /* Soft blur */
                z-index: 0;
                opacity: 0.6; /* Ensure it's visible but not distracting */
            }
            .pattern-top-left {
                top: 10%;
                left: 5%;
                transform: rotate(30deg);
            }
            .pattern-bottom-right {
                bottom: 10%;
                right: 5%;
                transform: rotate(-45deg);
            }
            .pattern-middle-left {
                top: 40%;
                left: 10%;
                transform: rotate(60deg);
                width: 100px;
                height: 100px;
                border-radius: 50%;
            }
            .pattern-middle-right {
                top: 60%;
                right: 10%;
                transform: rotate(-20deg);
                width: 120px;
                height: 120px;
                border-radius: 0;
            }


            .timeline-item {
              position: relative;
              margin-bottom: 4rem; /* Space between items */
              display: flex;
              align-items: center; /* Vertical centering of indicator and card */
              min-height: 120px; /* Ensure enough height for the items */
            }

            .timeline-item:last-child {
              margin-bottom: 0;
            }

            .step-indicator {
              width: 60px;
              height: 60px;
              border-radius: 50%;
              z-index: 3; /* Highest z-index to be on top of lines */
              flex-shrink: 0;
              text-shadow: 0 0 5px rgba(0,0,0,0.5);
              position: absolute; /* Position relative to .timeline */
              left: 50%;
              transform: translateX(-50%); /* Centered on the line */
              top: 50%; /* Vertical center of the item */
              transform: translate(-50%, -50%); /* Adjust for both horizontal and vertical centering */
            }
            
            /* The main glowing dot on the center line (behind the number) */
            .step-indicator::after {
              content: '';
              position: absolute;
              width: 20px;
              height: 20px;
              background: rgba(${accentRgb}, 1);
              border-radius: 50%;
              box-shadow: 0 0 20px rgba(${accentRgb}, 1), 0 0 30px rgba(${accentRgb}, 0.6);
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              z-index: -1; /* Behind the number */
            }

            .process-card {
              position: relative; /* For the new snake line elements */
              width: calc(50% - 100px); /* 50% width minus buffer for indicator + gap */
              min-height: 120px; /* Ensure cards have consistent height */
              z-index: 1; /* Below indicator, above background */
              text-align: center; /* Center content in cards by default */
            }
            
            .process-card h6, .process-card p {
                text-align: center !important; /* Force center alignment for text */
            }

            /* Snake Line Elements (now direct children of step-indicator) */
            .snake-horizontal, .snake-vertical {
                position: absolute;
                background-image: radial-gradient(circle, rgba(${accentRgb}, 0.9) 20%, transparent 20%);
                box-shadow: 0 0 10px rgba(${accentRgb}, 0.6);
                z-index: -2; /* Ensure they are behind the indicator */
            }

            .snake-horizontal {
                height: 3px; /* Thicker line for better visibility */
                top: 50%;
                transform: translateY(-50%);
                transform-origin: center center; /* Default for GSAP animation */
            }

            .snake-vertical {
                width: 3px; /* Thicker line for better visibility */
                left: 50%;
                transform: translateX(-50%);
                transform-origin: center center; /* Default for GSAP animation */
            }


            /* Left-aligned item styles */
            .timeline-item.left-aligned {
                justify-content: flex-start; /* Pushes content to the left of the main line */
            }
            .timeline-item.left-aligned .process-card {
                margin-right: auto; /* Push card to left */
                margin-left: 0;
                /* Adjust transform to correctly position the card relative to the center line */
                transform: translateX(calc(-50% + 150px)); /* Example: (half screen width - half card width) from center line */
            }

            .timeline-item.left-aligned .snake-horizontal {
                width: calc(50% - 30px); /* Extends from indicator to edge of timeline container */
                right: calc(50% + 30px); /* Position it from the right of the indicator to the card side */
                transform-origin: 100% 50%; /* Grow from right to left */
            }
            .timeline-item.left-aligned .snake-vertical {
                height: 60px; /* Vertical segment length */
                top: -60px; /* Position above indicator */
                left: 50%; /* Align with indicator */
                transform-origin: 50% 100%; /* Grow from bottom to top */
            }


            /* Right-aligned item styles */
            .timeline-item.right-aligned {
                justify-content: flex-end; /* Pushes content to the right of the main line */
            }
            .timeline-item.right-aligned .process-card {
                margin-left: auto; /* Push card to right */
                margin-right: 0;
                /* Adjust transform to correctly position the card relative to the center line */
                transform: translateX(calc(50% - 150px));
            }

            .timeline-item.right-aligned .snake-horizontal {
                width: calc(50% - 30px);
                left: calc(50% + 30px); /* Position it from the left of the indicator to the card side */
                transform-origin: 0% 50%; /* Grow from left to right */
            }
            .timeline-item.right-aligned .snake-vertical {
                height: 60px;
                top: -60px;
                left: 50%;
                transform-origin: 50% 100%; /* Grow from bottom to top */
            }


            /* Enhanced hover for process cards */
            .process-card:hover {
              transform: translateY(-8px); /* Lift more */
              box-shadow: 0 16px 30px rgba(0, 160, 233, 0.3); /* Stronger shadow */
            }


            /* Responsive adjustments for smaller screens */
            @media (max-width: 991.98px) {
              .timeline {
                padding: 30px 0;
              }
              .timeline-vertical-line {
                left: 50%;
                transform: translateX(-50%);
              }
              .timeline-bg-pattern {
                  display: none;
              }
              .timeline-item {
                flex-direction: column;
                align-items: center;
                margin-bottom: 2.5rem;
                justify-content: center; /* Center the entire item block */
                transform: translateX(0) !important; /* Reset transform on mobile */
              }
              .timeline-item.left-aligned,
              .timeline-item.right-aligned {
                padding: 0; /* Remove horizontal padding */
                justify-content: center; /* Center items on mobile */
              }
              .timeline-item .step-indicator {
                position: relative; /* Allow it to flow naturally */
                left: auto;
                transform: none;
                margin-bottom: 1rem; /* Space below indicator */
              }
              .process-card {
                width: 100%;
                max-width: 90%;
                margin: 0 auto !important; /* Center card */
                transform: translateX(0) !important; /* Reset transform on mobile */
                text-align: center;
              }
              /* Hide snake lines on mobile for a cleaner look */
              .snake-horizontal, .snake-vertical {
                  display: none;
              }
            }
          `}
        </style>
      </section>

      {/* Final CTA Section (No change) */}
      <section
        className="text-center text-white"
        style={{
          padding: "100px 20px"
        }}
      >
        {/* Your CTA content */}
      </section>
    </div>
  );
};

export default PhysicalDesignPage;