import React from "react";
import { <PERSON> } from "react-router-dom";
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "../Asserts/Makonis-Logo.png";

const Footer = () => {
  return (
    <footer className="bg-white text-gray-700">
      <div className="max-w-7xl mx-auto px-4 py-16">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-12">
          {/* Logo + social icons */}
          <div className="flex flex-col items-center md:items-start">
            <img
              src={MakonisLogo}
              alt="Makonis Logo"
              className="h-28 mb-6"
            />
            <div className="flex space-x-6 justify-center md:justify-start">
              {[
                { label: "Facebook", href: "#", icon: "fab fa-facebook-f" },
                { label: "Instagram", href: "#", icon: "fab fa-instagram" },
                { label: "Twitter", href: "#", icon: "fab fa-twitter" },
                { label: "LinkedIn", href: "#", icon: "fab fa-linkedin-in" },
              ].map((social) => (
                <a
                  key={social.label}
                  href={social.href}
                  className="text-gray-500 hover:text-blue-600 transition-colors duration-300"
                  aria-label={social.label}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <i className={`${social.icon} text-4xl`}></i>
                </a>
              ))}
            </div>
          </div>

          {/* About Us */}
          <div>
            <h5 className="text-gray-900 text-xl font-bold mb-6">
              ABOUT US
            </h5>
            <ul className="space-y-3 text-lg">
              {[
                { label: "Company", href: "/#" },
                { label: "Our Team", href: "/#" },
                { label: "Careers", href: "/#" },
                { label: "Blog", href: "/#" },
              ].map((link) => (
                <li key={link.label}>
                  <Link
                    to={link.href}
                    className="hover:text-blue-600 transition-colors duration-300"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Services */}
          <div>
            <h5 className="text-gray-900 text-xl font-bold mb-6">
              SERVICES
            </h5>
            <ul className="space-y-3 text-lg">
              {[
                { label: "Artificial Intelligence", to: "/ai" },
                { label: "Data Analytics", to: "/analytics" },
                { label: "IoT Solutions", to: "/iot" },
                { label: "Web & Mobile Dev", to: "/webdev" },
                { label: "Testing Services", to: "/testing" },
                { label: "Embedded Systems", to: "/embedded" },
              ].map((service) => (
                <li key={service.label}>
                  <Link
                    to={service.to}
                    className="hover:text-blue-600 transition-colors duration-300"
                  >
                    {service.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Divider */}
        <div className="mt-12 border-t border-gray-200 pt-6 text-center">
          <p className="text-gray-500 text-base">
            © {new Date().getFullYear()} Makonis Software Solutions. All Rights Reserved.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
