import React, { useRef, useEffect } from 'react';
import { useParams } from 'react-router-dom';

// Import video files
import video2 from "../assets/2.mp4";
import video10 from "../assets/10.mp4";
import video17 from "../assets/17.mp4";
import video27 from "../assets/27.mp4";
import MTI from "../assets/MTI.mp4";
import ATS from "../assets/ATS.mp4";
import ATS1 from "../assets/ATS1.mp4";
import Makoplus from "../assets/Makoplus.mp4";
import homePageVideo1 from "../assets/homepagevideo1.mp4";
import homePageVideo2 from "../assets/homepagevideo2.mp4";

// Video array
const allVideos = [
  { id: 'memories', src: MTI, title: 'Makonis Memories' },
  { id: 'ats', src: ATS, title: 'ATS Demo' },
  { id: 'ats1', src: ATS1, title: 'ATS Demo Extended' },
  { id: 'makoplus', src: Makoplus, title: 'Mako Plus' },
  { id: 'home1', src: homePageVideo1, title: 'Company Overview' },
  { id: 'home2', src: homePageVideo2, title: 'Our Journey' },
  { id: 'video2', src: video2, title: 'Team Video 1' },
  { id: 'video10', src: video10, title: 'Team Video 2' },
  { id: 'video17', src: video17, title: 'Team Video 3' },
  { id: 'video27', src: video27, title: 'Team Video 4' },
];

const VideoViewerPage = () => {
  const { videoId } = useParams();
  const videoRef = useRef(null);

  // Find current video
  const currentVideo = allVideos.find(video => video.id === videoId) || allVideos[0];

  useEffect(() => {
    // Auto-play and enter fullscreen when component mounts
    if (videoRef.current) {
      videoRef.current.play().catch(console.error);
      
      // Request fullscreen after a short delay to ensure video is loaded
      setTimeout(() => {
        if (videoRef.current && videoRef.current.requestFullscreen) {
          videoRef.current.requestFullscreen().catch(console.error);
        } else if (videoRef.current && videoRef.current.webkitRequestFullscreen) {
          videoRef.current.webkitRequestFullscreen();
        } else if (videoRef.current && videoRef.current.msRequestFullscreen) {
          videoRef.current.msRequestFullscreen();
        }
      }, 500);
    }
  }, []);

  // Keyboard controls
  useEffect(() => {
    const handleKeyPress = (e) => {
      if (!videoRef.current) return;

      switch (e.key) {
        case ' ':
          e.preventDefault();
          if (videoRef.current.paused) {
            videoRef.current.play();
          } else {
            videoRef.current.pause();
          }
          break;
        case 'f':
        case 'F':
          if (videoRef.current.requestFullscreen) {
            videoRef.current.requestFullscreen();
          }
          break;
        case 'Escape':
          window.close();
          break;
        case 'ArrowLeft':
          videoRef.current.currentTime -= 10; // Skip back 10 seconds
          break;
        case 'ArrowRight':
          videoRef.current.currentTime += 10; // Skip forward 10 seconds
          break;
        case 'ArrowUp':
          e.preventDefault();
          videoRef.current.volume = Math.min(1, videoRef.current.volume + 0.1);
          break;
        case 'ArrowDown':
          e.preventDefault();
          videoRef.current.volume = Math.max(0, videoRef.current.volume - 0.1);
          break;
        default:
          break;
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, []);

  return (
    <div className="video-viewer">
      <div className="video-header">
        <h1 className="video-title">{currentVideo.title}</h1>
        <div className="video-controls-header">
          <button 
            onClick={() => videoRef.current?.requestFullscreen()} 
            className="control-btn"
          >
            🔳 Fullscreen
          </button>
          <button onClick={() => window.close()} className="close-btn">
            ✕ Close
          </button>
        </div>
      </div>

      <div className="video-container">
        <video
          ref={videoRef}
          src={currentVideo.src}
          controls
          className="main-video"
          onLoadedData={() => {
            // Ensure video is ready before attempting fullscreen
            console.log('Video loaded');
          }}
        >
          Your browser does not support the video tag.
        </video>
      </div>

      <div className="video-info">
        <h2>{currentVideo.title}</h2>
        <div className="keyboard-shortcuts">
          <h3>Keyboard Shortcuts:</h3>
          <div className="shortcuts-grid">
            <span>Space: Play/Pause</span>
            <span>F: Fullscreen</span>
            <span>← →: Skip 10s</span>
            <span>↑ ↓: Volume</span>
            <span>Esc: Close</span>
          </div>
        </div>
      </div>

      <style>{`
        .video-viewer {
          background: #000;
          color: white;
          min-height: 100vh;
          display: flex;
          flex-direction: column;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .video-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 1rem 2rem;
          background: rgba(0, 0, 0, 0.9);
          backdrop-filter: blur(10px);
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .video-title {
          font-size: 1.5rem;
          margin: 0;
          background: linear-gradient(135deg, #ffffff 0%, #009DE6 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }

        .video-controls-header {
          display: flex;
          gap: 1rem;
          align-items: center;
        }

        .control-btn, .close-btn {
          padding: 0.5rem 1rem;
          background: linear-gradient(135deg, #002B59 0%, #009DE6 100%);
          color: white;
          border: none;
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.3s ease;
          font-size: 0.9rem;
        }

        .control-btn:hover, .close-btn:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 157, 230, 0.4);
        }

        .video-container {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 2rem;
          background: #000;
        }

        .main-video {
          width: 100%;
          max-width: 100%;
          max-height: 80vh;
          border-radius: 8px;
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
        }

        .video-info {
          padding: 2rem;
          background: rgba(0, 0, 0, 0.9);
          border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .video-info h2 {
          margin: 0 0 1rem 0;
          color: #009DE6;
        }

        .keyboard-shortcuts h3 {
          margin: 0 0 0.5rem 0;
          color: #ffffff;
          font-size: 1rem;
        }

        .shortcuts-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
          gap: 0.5rem;
          font-size: 0.9rem;
          opacity: 0.8;
        }

        .shortcuts-grid span {
          padding: 0.25rem 0.5rem;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 4px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
          .video-header {
            flex-direction: column;
            gap: 1rem;
            padding: 1rem;
          }
          
          .video-title {
            font-size: 1.2rem;
          }
          
          .video-controls-header {
            gap: 0.5rem;
          }
          
          .control-btn, .close-btn {
            padding: 0.4rem 0.8rem;
            font-size: 0.8rem;
          }
          
          .video-container {
            padding: 1rem;
          }
          
          .main-video {
            max-height: 70vh;
          }
          
          .shortcuts-grid {
            grid-template-columns: 1fr 1fr;
            font-size: 0.8rem;
          }
        }

        /* Fullscreen styles */
        .main-video:fullscreen {
          width: 100vw;
          height: 100vh;
          max-width: none;
          max-height: none;
          border-radius: 0;
        }

        .main-video:-webkit-full-screen {
          width: 100vw;
          height: 100vh;
          max-width: none;
          max-height: none;
          border-radius: 0;
        }

        .main-video:-moz-full-screen {
          width: 100vw;
          height: 100vh;
          max-width: none;
          max-height: none;
          border-radius: 0;
        }
      `}</style>
    </div>
  );
};

export default VideoViewerPage;
